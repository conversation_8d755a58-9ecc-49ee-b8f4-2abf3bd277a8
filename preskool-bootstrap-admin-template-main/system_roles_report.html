{% extends 'base.html' %}
{% load static %}

{% block title %}System Roles Report{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'system_reports' %}">System Reports</a></li>
                <li class="breadcrumb-item active">System Roles Report</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-success" role="alert">
    <strong>System Roles Report</strong> - Detailed analysis of system roles, permissions, and access levels.
        </div>

        <!-- Generate Report Button -->
        <div class="row mb-4">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body text-center">
                        <button id="generateRolesReport" class="btn btn-success btn-lg">
                            <i class="fas fa-chart-pie me-2"></i>Generate System Roles Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Results -->
        <div class="row" id="rolesReportResults" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-user-tag me-2"></i>System Roles Analysis</h6>
                        <button class="btn btn-light btn-sm" onclick="exportRolesReport()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="rolesReportTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Role Name</th>
                                        <th>Description</th>
                                        <th>Permission Level</th>
                                        <th>User Count</th>
                                        <th>Key Permissions</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="rolesReportBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Distribution Chart -->
        <div class="row mt-4" id="roleDistribution" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-chart-donut me-2"></i>Role Distribution</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <canvas id="roleChart" width="400" height="200"></canvas>
                            </div>
                            <div class="col-md-6">
                                <div id="roleStats"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
    const generateBtn = document.getElementById('generateRolesReport');
    const resultsSection = document.getElementById('rolesReportResults');
    const distributionSection = document.getElementById('roleDistribution');
    const resultsBody = document.getElementById('rolesReportBody');

    async function generateRolesReport() {
        try {
            const response = await fetch('/api/reports/roles/');
            if (!response.ok) throw new Error('Network response was not ok');
            return await response.json();
        } catch (error) {
            console.error('Error:', error);
            return { results: [] };
        }
    }

    function renderRolesResults(data) {
        if (!data.results || !data.results.length) {
            resultsBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No roles found</td></tr>';
            return;
        }

        resultsBody.innerHTML = data.results.map((role, index) => {
            const statusBadge = role.is_active ? 
                '<span class="badge bg-success">Active</span>' : 
                '<span class="badge bg-danger">Inactive</span>';
            
            const levelBadge = `<span class="badge bg-${role.level_color || 'secondary'}">${role.permission_level}</span>`;
            
            return `
                <tr>
                    <td>${index + 1}</td>
                    <td><strong>${role.name}</strong></td>
                    <td>${role.description}</td>
                    <td>${levelBadge}</td>
                    <td><span class="badge bg-primary">${role.user_count}</span></td>
                    <td><small>${role.key_permissions.join(', ')}</small></td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        }).join('');

        // Update role statistics
        updateRoleStats(data.results);
    }

    function updateRoleStats(roles) {
        const statsDiv = document.getElementById('roleStats');
        const totalUsers = roles.reduce((sum, role) => sum + role.user_count, 0);
        
        statsDiv.innerHTML = `
            <h6>Role Statistics</h6>
            <ul class="list-unstyled">
                <li><strong>Total Roles:</strong> ${roles.length}</li>
                <li><strong>Total Users:</strong> ${totalUsers}</li>
                <li><strong>Active Roles:</strong> ${roles.filter(r => r.is_active).length}</li>
                <li><strong>Admin Roles:</strong> ${roles.filter(r => r.permission_level === 'High').length}</li>
            </ul>
        `;
    }

    generateBtn.addEventListener('click', async function() {
        resultsSection.style.display = 'block';
        distributionSection.style.display = 'block';
        
        const data = await generateRolesReport();
        renderRolesResults(data);
    });
});

function exportRolesReport() {
    alert('Export roles report functionality would be implemented here');
}
</script>
{% endblock %}
