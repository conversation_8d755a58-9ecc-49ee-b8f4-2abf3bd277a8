{% extends 'base.html' %}
{% load static %}

{% block title %}Search User{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item active">Search user</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-info" role="alert">
    Search for users in the system by entering a full name or either first or last name to view their details.

        </div>

        <div class="row">
    <div class="col-xs-12 col-sm-12">
        <form class="form-horizontal" id="user-search-form">
            <div class="form-group row align-items-center">
                <label class="col-sm-3 col-form-label text-sm-end">Staff Name</label>
                <div class="col-sm-5">
                    <input id="staffQuery" class="form-control" type="text" placeholder="Enter staff first or last name">
                </div>
                <div class="col-sm-4 text-sm-start text-end mt-3 mt-sm-0">
                    <button type="submit" class="btn btn-info">
                        Search <i class="fa fa-search ms-1"></i>
                    </button>
                                </div>
                            </div>
                        </form>
            </div>
        </div>

        <div class="row mt-4" id="resultsSection" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">Search Results</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="resultsTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Full Name</th>
                                        <th>Post</th>
                                        <th>Department/Section</th>
                                        <th>Campus</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsBody"></tbody>
                        </table>
                    </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('user-search-form');
  const queryInput = document.getElementById('staffQuery');
  const resultsSection = document.getElementById('resultsSection');
  const resultsBody = document.getElementById('resultsBody');

  async function fetchResults(q) {
    try {
      const resp = await fetch(`/api/staff/search/?q=${encodeURIComponent(q)}`);
      if (!resp.ok) throw new Error('Network response was not ok');
      return await resp.json();
    } catch (e) {
      return { results: [] };
    }
  }

  function renderNoResults() {
    resultsBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No users found</td></tr>';
  }

  form.addEventListener('submit', async function (e) {
    e.preventDefault();
    const q = (queryInput.value || '').trim();
    resultsSection.style.display = 'block';

    const data = await fetchResults(q);
    if (!data.results || !data.results.length) {
      renderNoResults();
      return;
    }
    resultsBody.innerHTML = data.results.map((r, i) => (
      '<tr>' +
      `<td>${i + 1}</td>` +
      `<td>${r.full_name}</td>` +
      `<td>${r.post}</td>` +
      `<td>${r.department}</td>` +
      `<td>${r.campus}</td>` +
      '<td><a href="javascript:void(0);" class="btn btn-success btn-sm">View Details</a></td>' +
      '</tr>'
    )).join('');
  });
});
</script>
{% endblock %}