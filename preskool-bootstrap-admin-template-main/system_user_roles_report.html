{% extends 'base.html' %}
{% load static %}

{% block title %}System User Roles Report{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item"><a href="{% url 'system_reports' %}">System Reports</a></li>
                <li class="breadcrumb-item active">System User Roles Report</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-warning" role="alert">
    <strong>System User Roles Report</strong> - View the relationship between users and their assigned system roles.
        </div>

        <!-- Filter and Generate Section -->
        <div class="row mb-4">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Report Configuration</h6>
                    </div>
                    <div class="card-body">
                        <form id="userRolesReportForm" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Filter by Role</label>
                                <select class="form-select" id="roleFilter">
                                    <option value="">All Roles</option>
                                    <option value="Administrator">Administrator</option>
                                    <option value="Moderator">Moderator</option>
                                    <option value="User">User</option>
                                    <option value="Guest">Guest</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">View Type</label>
                                <select class="form-select" id="viewType">
                                    <option value="user_roles">Users with Roles</option>
                                    <option value="role_users">Roles with Users</option>
                                    <option value="summary">Summary View</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-warning d-block">
                                    <i class="fas fa-chart-bar me-1"></i>Generate Report
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Results -->
        <div class="row" id="userRolesResults" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="fas fa-user-cog me-2"></i>User Roles Report Results</h6>
                        <button class="btn btn-dark btn-sm" onclick="exportUserRolesReport()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="userRolesTable">
                                <thead id="userRolesTableHead">
                                    <!-- Dynamic headers based on view type -->
                                </thead>
                                <tbody id="userRolesBody"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mt-4" id="summaryStats" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>Role Assignment Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="statsContent">
                            <!-- Dynamic statistics content -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('userRolesReportForm');
    const resultsSection = document.getElementById('userRolesResults');
    const summarySection = document.getElementById('summaryStats');
    const tableHead = document.getElementById('userRolesTableHead');
    const tableBody = document.getElementById('userRolesBody');

    async function generateUserRolesReport(filters) {
        try {
            const params = new URLSearchParams(filters);
            const response = await fetch(`/api/reports/user-roles/?${params}`);
            if (!response.ok) throw new Error('Network response was not ok');
            return await response.json();
        } catch (error) {
            console.error('Error:', error);
            return { results: [], view_type: filters.view_type };
        }
    }

    function renderUserRolesResults(data, viewType) {
        // Set table headers based on view type
        let headers = '';
        if (viewType === 'user_roles') {
            headers = `
                <tr>
                    <th>#</th>
                    <th>User Name</th>
                    <th>Post</th>
                    <th>Department</th>
                    <th>Assigned Role</th>
                    <th>Role Level</th>
                    <th>Assignment Date</th>
                    <th>Status</th>
                </tr>
            `;
        } else if (viewType === 'role_users') {
            headers = `
                <tr>
                    <th>#</th>
                    <th>Role Name</th>
                    <th>Permission Level</th>
                    <th>Assigned Users</th>
                    <th>User Count</th>
                    <th>Status</th>
                </tr>
            `;
        } else {
            headers = `
                <tr>
                    <th>Role</th>
                    <th>User Count</th>
                    <th>Percentage</th>
                    <th>Status</th>
                </tr>
            `;
        }
        tableHead.innerHTML = headers;

        if (!data.results || !data.results.length) {
            const colSpan = viewType === 'summary' ? 4 : (viewType === 'role_users' ? 6 : 8);
            tableBody.innerHTML = `<tr><td colspan="${colSpan}" class="text-center text-muted">No data found</td></tr>`;
            return;
        }

        // Render table body based on view type
        if (viewType === 'user_roles') {
            tableBody.innerHTML = data.results.map((item, index) => {
                const statusBadge = item.is_active ? 
                    '<span class="badge bg-success">Active</span>' : 
                    '<span class="badge bg-danger">Inactive</span>';
                const levelBadge = `<span class="badge bg-${item.role_level_color || 'secondary'}">${item.role_level}</span>`;
                
                return `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.user_name}</td>
                        <td>${item.post}</td>
                        <td>${item.department}</td>
                        <td><strong>${item.role_name}</strong></td>
                        <td>${levelBadge}</td>
                        <td>${item.assignment_date || 'N/A'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            }).join('');
        } else if (viewType === 'role_users') {
            tableBody.innerHTML = data.results.map((item, index) => {
                const statusBadge = item.is_active ? 
                    '<span class="badge bg-success">Active</span>' : 
                    '<span class="badge bg-danger">Inactive</span>';
                
                return `
                    <tr>
                        <td>${index + 1}</td>
                        <td><strong>${item.role_name}</strong></td>
                        <td><span class="badge bg-${item.level_color || 'secondary'}">${item.permission_level}</span></td>
                        <td><small>${item.users.join(', ')}</small></td>
                        <td><span class="badge bg-primary">${item.user_count}</span></td>
                        <td>${statusBadge}</td>
                    </tr>
                `;
            }).join('');
        } else {
            tableBody.innerHTML = data.results.map((item) => {
                return `
                    <tr>
                        <td><strong>${item.role_name}</strong></td>
                        <td><span class="badge bg-primary">${item.user_count}</span></td>
                        <td>${item.percentage}%</td>
                        <td><span class="badge bg-${item.is_active ? 'success' : 'danger'}">${item.is_active ? 'Active' : 'Inactive'}</span></td>
                    </tr>
                `;
            }).join('');
        }

        // Show summary statistics
        updateSummaryStats(data.summary || {});
    }

    function updateSummaryStats(summary) {
        const statsContent = document.getElementById('statsContent');
        statsContent.innerHTML = `
            <div class="col-md-3 text-center">
                <h4 class="text-primary">${summary.total_assignments || 0}</h4>
                <p class="text-muted">Total Assignments</p>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-success">${summary.active_assignments || 0}</h4>
                <p class="text-muted">Active Assignments</p>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-warning">${summary.unique_roles || 0}</h4>
                <p class="text-muted">Unique Roles</p>
            </div>
            <div class="col-md-3 text-center">
                <h4 class="text-info">${summary.users_with_roles || 0}</h4>
                <p class="text-muted">Users with Roles</p>
            </div>
        `;
        summarySection.style.display = 'block';
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const filters = {
            role: document.getElementById('roleFilter').value,
            view_type: document.getElementById('viewType').value
        };

        resultsSection.style.display = 'block';
        const data = await generateUserRolesReport(filters);
        renderUserRolesResults(data, filters.view_type);
    });
});

function exportUserRolesReport() {
    alert('Export user roles report functionality would be implemented here');
}
</script>
{% endblock %}
