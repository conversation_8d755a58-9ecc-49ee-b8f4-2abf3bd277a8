{% extends 'base.html' %}
{% load static %}

{% block title %}User System{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item active">User System</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-primary" role="alert">
    <strong>User System Management</strong> - Manage user accounts, permissions, and system access. Search for users to perform various system operations.
        </div>

        <div class="row">
    <div class="col-xs-12 col-sm-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">Search Users in System</h6>
            </div>
            <div class="card-body">
                <form class="form-horizontal" id="system-search-form" method="POST">
                    {% csrf_token %}
                    <div class="form-group row align-items-center">
                        <label class="col-sm-3 col-form-label text-sm-end">Staff Name</label>
                        <div class="col-sm-5">
                            <input id="systemQuery" name="staff_name" class="form-control" type="text" placeholder="Enter staff first or last name" required>
                            <small class="text-muted"></small>
                        </div>
                        <div class="col-sm-4 text-sm-start text-end mt-3 mt-sm-0">
                            <button type="submit" class="btn btn-primary">
                                Search System <i class="fa fa-cogs ms-1"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

        <div class="row mt-4" id="systemResultsSection" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">System User Results</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="systemResultsTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Full Name</th>
                                        <th>Post</th>
                                        <th>Department/Section</th>
                                        <th>Campus</th>
                                        <th>System Role</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="systemResultsBody"></tbody>
                        </table>
                    </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('system-search-form');
  const queryInput = document.getElementById('systemQuery');
  const resultsSection = document.getElementById('systemResultsSection');
  const resultsBody = document.getElementById('systemResultsBody');

  async function fetchSystemResults(q) {
    try {
      const resp = await fetch(`/api/staff/system/?q=${encodeURIComponent(q)}`);
      if (!resp.ok) throw new Error('Network response was not ok');
      return await resp.json();
    } catch (e) {
      return { results: [] };
    }
  }

  function renderNoResults() {
    resultsBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No users found in system</td></tr>';
  }

  form.addEventListener('submit', async function (e) {
    e.preventDefault();
    const q = (queryInput.value || '').trim();
    resultsSection.style.display = 'block';

    const data = await fetchSystemResults(q);
    if (!data.results || !data.results.length) {
      renderNoResults();
      return;
    }
    resultsBody.innerHTML = data.results.map((r, i) => {
      const statusBadge = r.is_active ? 
        '<span class="badge bg-success">Active</span>' : 
        '<span class="badge bg-danger">Inactive</span>';
      
      const roleBadge = `<span class="badge bg-info">${r.system_role || 'User'}</span>`;
      
      return (
        '<tr>' +
        `<td>${i + 1}</td>` +
        `<td>${r.full_name}</td>` +
        `<td>${r.post}</td>` +
        `<td>${r.department}</td>` +
        `<td>${r.campus}</td>` +
        `<td>${roleBadge}</td>` +
        `<td>${statusBadge}</td>` +
        '<td><a href="javascript:void(0);" class="btn btn-warning btn-sm">Manage System</a></td>' +
        '</tr>'
      );
    }).join('');
  });
});
</script>
{% endblock %}
