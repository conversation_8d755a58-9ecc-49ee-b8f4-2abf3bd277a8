{% extends "users/base.html" %} {% load static %} {% block content %}

<div class="main-wrapper">
  <
    

    



  <div class="page-wrapper">
    <div class="content container-fluid">
      <div class="page-header">
        <div class="row align-items-center">
          <div class="col-sm-12">
            <div class="page-sub-header">
              <h3 class="page-title">Add User</h3>  
              <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                <li class="breadcrumb-item active">Add User</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-12">
          <div class="alert alert-success" role="alert">
            To add a user, please search the user (staff) in the system by
            filling in the form below with a full or either first or lastname.
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-12">
          <div class="card comman-shadow">
            <div class="card-body">
              <form id="user-search-form">
                <div class="row align-items-end">
                  <div class="col-md-4">
                    <div class="form-group local-forms">
                      <label>Staff Name</label>
                      <input
                        id="staffQuery"
                        class="form-control"
                        type="text"
                        placeholder="Enter staff first or last name"
                      />
                      <small class="text-muted"
                        >Try typing "wakisa" then click Get user</small
                      >
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                      Get user <span class="ms-1">➜</span>
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-4" id="resultsSection" style="display: none">
        <div class="col-sm-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Search Results</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped" id="resultsTable">
                  <thead>
                    <tr>
                      <th>#</th>
                      <th>Full Name</th>
                      <th>Post</th>
                      <th>Department/Section</th>
                      <th>Campus</th>
                      <th>Action</th>
                    </tr>
                    <thead></thead>
                  </thead>

                  <tbody id="resultsBody"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  {% endblock content %}
</div>
