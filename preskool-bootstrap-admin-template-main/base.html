<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>{% block title %}Site{% endblock %}</title>
  {% load static %}
  <link rel="icon" href="{% static 'img/favicon.png' %}">
  <link rel="stylesheet" href="{% static 'plugins/bootstrap/css/bootstrap.min.css' %}">
  <link rel="stylesheet" href="{% static 'plugins/feather/feather.css' %}">
  <link rel="stylesheet" href="{% static 'plugins/icons/flags/flags.css' %}">
  <link rel="stylesheet" href="{% static 'plugins/fontawesome/css/fontawesome.min.css' %}">
  <link rel="stylesheet" href="{% static 'plugins/fontawesome/css/all.min.css' %}">
  <link rel="stylesheet" href="{% static 'css/style.css' %}">
</head>
<body>
  <div class="main-wrapper">

    <div class="header">
      <div class="header-left">
        <a href="{% url 'home' %}" class="logo">
          <img src="{% static 'img/logo.png' %}" alt="Logo">
        </a>
        <a href="{% url 'home' %}" class="logo logo-small">
          <img src="{% static 'img/logo-small.png' %}" alt="Logo" width="30" height="30">
        </a>
      </div>
      <div class="menu-toggle">
        <a href="javascript:void(0);" id="toggle_btn"><i class="fas fa-bars"></i></a>
      </div>
      <a class="mobile_btn" id="mobile_btn"><i class="fas fa-bars"></i></a>
      <ul class="nav user-menu">
        <li class="nav-item zoom-screen me-2">
          <a href="#" class="nav-link header-nav-list win-maximize">
            <img src="{% static 'img/icons/header-icon-04.svg' %}" alt="">
          </a>
        </li>
        <li class="nav-item dropdown has-arrow new-user-menus">
          <a href="#" class="dropdown-toggle nav-link" data-bs-toggle="dropdown">
            <span class="user-img">
              <img class="rounded-circle" src="{% static 'img/profiles/avatar-01.jpg' %}" width="31" alt="User">
              <div class="user-text">
                <h6>Administrator</h6>
                <p class="text-muted mb-0">Admin</p>
              </div>
            </span>
          </a>
          <div class="dropdown-menu">
            <a class="dropdown-item" href="#">My Profile</a>
            <a class="dropdown-item" href="#">Inbox</a>
            <a class="dropdown-item" href="#">Logout</a>
          </div>
        </li>
      </ul>
    </div>

    <div class="sidebar" id="sidebar">
      <div class="sidebar-inner slimscroll">
        <div id="sidebar-menu" class="sidebar-menu">
          <ul>
            <li class="menu-title"><span>Main Menu</span></li>
            <li>
              <a href="{% url 'home' %}"><i class="feather-grid"></i> <span>Home</span></a>
            </li>
            <li class="active">
              <a href="{% url 'add_users' %}"><i class="fas fa-user-plus"></i> <span>Add User</span></a>
            </li>
            <li>
              <a href="#"><i class="fas fa-th-large"></i> <span>User Systems</span></a>
            </li>
            <li>
              <a href="#"><i class="fas fa-chart-bar"></i> <span>System Reports</span></a>
            </li>
            <li>
              <a href="#"><i class="fas fa-search"></i> <span>Search User</span></a>
            </li>
            <li>
              <a href="{% url 'password_reset' %}"><i class="fas fa-key"></i> <span>Password Reset</span></a>
            </li>
            <li>
              <a href="{% url 'deactivate_user' %}"><i class="fas fa-user-times"></i> <span>Deactivate User</span></a>
            </li>
            <li>
              <a href="{% url 'deactivate_status' %}"><i class="fas fa-user-slash"></i> <span>Deactivate Status</span></a>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="page-wrapper">
      <div class="content container-fluid">
        {% block content %}{% endblock %}
      </div>
      <footer>
      </footer>
    </div>

  </div>

  <script src="{% static 'js/jquery-3.6.0.min.js' %}"></script>
  <script src="{% static 'plugins/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
  <script src="{% static 'plugins/slimscroll/jquery.slimscroll.min.js' %}"></script>
  <script src="{% static 'js/feather.min.js' %}"></script>
  <script src="{% static 'js/script.js' %}"></script>
  {% block extra_js %}{% endblock %}
</body>
</html>
