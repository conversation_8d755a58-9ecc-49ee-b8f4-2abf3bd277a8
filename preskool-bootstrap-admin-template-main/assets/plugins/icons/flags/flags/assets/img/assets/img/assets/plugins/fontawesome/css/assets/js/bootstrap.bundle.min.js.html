<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

	<title>PreSkool -  School, Education admin HTML Template</title>
	<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.png">

	<!-- Meta -->
	<meta name="description" content="Preskool's latest school management admin template is a fully responsive and Bootstrap-based HTML5/CSS3 template. Also, it is available in different technology.">
	<meta name="keywords" content="school management template, education template, admin management template">
	<meta name="author" content="Dreamguys">

	<!-- Facebook Meta Tags -->
	<meta property="og:url" content="https://preschool.dreamguystech.com/">
	<meta property="og:type" content="website">
	<meta property="og:title" content="PreSkool -  School, Education admin HTML Template">
	<meta property="og:description" content="Preskool's latest school management admin template is a fully responsive and Bootstrap-based HTML5/CSS3 template. Also, it is available in different technology.">
	<meta property="og:image" content="/assets/img/preview-banner.jpg">

	<!-- Twitter Meta Tags -->
	<meta name="twitter:card" content="summary_large_image">
	<meta property="twitter:domain" content="https://preschool.dreamguystech.com/">
	<meta property="twitter:url" content="https://kofejob.dreamguystech.com/">
	<meta name="twitter:title" content="PreSkool -  School, Education admin HTML Template">
	<meta name="twitter:description" content="Preskool's latest school management admin template is a fully responsive and Bootstrap-based HTML5/CSS3 template. Also, it is available in different technology.">
	<meta name="twitter:image" content="/assets/img/preview-banner.jpg">

	<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,500;0,600;0,700;1,400&display=swap" rel="stylesheet"> 

	<link rel="stylesheet" href="assets/css/animate.css">
	<link rel="stylesheet" href="assets/css/bootstrap.min.css">
	<link rel="stylesheet" href="assets/css/materialdesignicons.min.css">
			
	<!-- Fontawesome CSS -->
	<link rel="stylesheet" href="assets/plugins/fontawesome/css/fontawesome.min.css">
	<link rel="stylesheet" href="assets/plugins/fontawesome/css/all.min.css">

	<link rel="stylesheet" href="assets/css/style.css">
</head>
	
<body>  

	<!-- Loader -->
	<div id="loader-wrapper">
		<div id="loader">
			<div class="loader-ellips">
				<span class="loader-ellips__dot"></span>
				<span class="loader-ellips__dot"></span>
				<span class="loader-ellips__dot"></span>
				<span class="loader-ellips__dot"></span>
			</div>
		</div>
	</div>
	
	<!-- Header -->
	<header id="home" class="header">

		<!-- Start Navigation -->
		<nav class="navbar navbar-expand-md home-menu hp4">
			<div class="container-fluid">

				<!-- Start Header Navigation -->
				<a class="logo-link smooth-menu header-logo" href="#home">
					<img src="assets/img/logo.png" class="logo logo-display" alt="Logo">
				</a>
				<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-menu">
					<i class="fa fa-bars"></i>
				</button>
				<!-- End Header Navigation -->

				<div class="collapse navbar-collapse mainmenu" id="navbar-menu">
					<ul class="nav navbar-nav ml-auto">
						<li class="nav-item">
							<a class="smooth-menu nav-link" href="#home">Home</a>
						</li>
						<li class="nav-item">
							<a class="smooth-menu nav-link" href="#homepage">Demo</a>
						</li>
						<li class="nav-item">
							<a class="smooth-menu nav-link" href="#pages">Pages</a>
						</li>
					</ul>
					<div class="nav-btn header-btn">
						<a href="https://themeforest.net/item/preskool-bootstrap-admin-html-template/29532878" target="_blank" class="button download-btn">Buy Template</a>
					</div>
				</div>
			</div>

		</nav>
		<!-- End Navigation -->

	</header>
	<!-- End Header -->

	<section class="hero-section">
		<div class="container-fluid">
			<div class="row align-items-center">
				<div class="col-lg-5 wow fadeInLeft" data-wow-delay="0.2s">
					<h1 style="color:#fff">Preskool</h1>
					<h2 style="color:#fff">Bootstrap HTML Admin Dashboard Template</h2>
					
					<a href="template/index.html" target="_blank" class="button">Live Demo</a>
					<a href="https://themeforest.net/item/preskool-bootstrap-admin-html-template/29532878" target="_blank" class="button">Buy Template</a>
				</div>
				<div class="col-lg-7 wow fadeInRight" data-wow-delay="0.4s">
					<div class="hero-4-img">
						<a href="" target="_blank"><img src="assets/img/image.png" alt=""></a>
					</div>
				</div>
			</div>
		</div>
	</section>
	
	<section id="homepage" class="home-page">
		<div class="container-fluid">
			<div class="row justify-content-center">
				<div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.2s">
					<div class="section-header text-center">
						<h2>Home Page</h2>
						<p class="sub-title">We build this template with HTML5,Angular,Vuejs and Laravel.</p>
					</div>
				</div>
			</div>
	
			<div class="row demo-row justify-content-center">
				<div class="col-md-6 col-sm-6 col-12">
					<div class="demo-wrap mb-0" data-aos="fade-up">
						<div class="demo-tabs">
							<ul class="nav nav-tabs">
								<li class="nav-item"><a target="_blank" href="template/index.html">HTML5</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/laravel/template/public/">Laravel</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/vuejs/template/index">Vuejs</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/angular/preskool/dashboard/dashboard-main">Angular</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/react/dashboard">Reactjs</a></li>
							</ul>
						</div>
								<div class="demo-box">
									<a href="template/index.html" target="_blank" href=""><img class="img-fluid" src="assets/img/image.png" alt=""></a>
								</div>
								<h5 class="text-center mt-3">Dashboard</h5>
					</div>
				</div>
				<div class="col-md-6 col-sm-6 col-12">
					<div class="demo-wrap mb-0" data-aos="fade-up">
						<div class="demo-tabs">
							<ul class="nav nav-tabs">
								<li class="nav-item"><a target="_blank" href="template-rtl/index.html">HTML5</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/laravel/template-rtl/public/">Laravel</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/vuejs/template-rtl/index">Vuejs</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/angular/preskool-rtl/dashboard/dashboard-main">Angular</a></li>
								<li class="nav-item"><a target="_blank" href="https://preschool.dreamguystech.com/react-rtl/dashboard">ReactJs</a></li>
							</ul>
						</div>
								<div class="demo-box">
									<a href="template-rtl/index.html" target="_blank" href=""><img class="img-fluid" src="assets/img/home.jpg" alt=""></a>
								</div>
								<h5 class="text-center mt-3">Dashboard RTL</h5>
					</div>
				</div>
			</div>
		</div>
	</section>
	
	<section id="pages" class="doctor-features">
		<div class="container-fluid">
			<div class="row justify-content-center">
				<div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.2s">
					<div class="section-header text-center">
						<h2>Preskool Pages</h2>
						<p class="sub-title">Preskool template includes various features for providers such as student list, teachers list, department,etc.</p>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" target="_blank" href="template/teachers.html"> View Demo</a></div>
						<a class="overlay-link" href="template/teachers.html" target="_blank"><img src="assets/img/image-1.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/teachers.html" target="_blank">Teachers List</a></div>
					</div>
				</div>

				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/teacher-details.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/teacher-details.html" target="_blank"><img src="assets/img/image-2.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/teacher-details.html" target="_blank">Teachers View</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/departments.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/departments.html" target="_blank"><img src="assets/img/image-3.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/departments.html" target="_blank">Departments</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/subjects.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/subjects.html" target="_blank"><img src="assets/img/image-4.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/subjects.html" target="_blank">Subjects</a></div>
					</div>
				</div>

				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/fees-collections.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/fees-collections.html" target="_blank"><img src="assets/img/image-5.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/fees-collections.html" target="_blank">Fees</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/expenses.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/expenses.html" target="_blank"><img src="assets/img/image-6.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/expenses.html" target="_blank"> Expenses</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/salary.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/salary.html" target="_blank"><img src="assets/img/image-7.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/salary.html" target="_blank">Salary</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/holiday.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/holiday.html" target="_blank"><img src="assets/img/image-8.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/holiday.html" target="_blank">Holiday</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/event.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/event.html" target="_blank"><img src="assets/img/image-9.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/event.html" target="_blank">Events</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template/library.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template/library.html" target="_blank"><img src="assets/img/image-10.png" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template/library.html" target="_blank">Library</a></div>
					</div>
				</div>
			</div>
			
			<div class="row justify-content-center">
				<div class="col-lg-8 text-center wow fadeInRight" data-wow-delay="0.2s">
					<div class="section-header text-center">
						<h2>Preskool RTL Pages</h2>
						<p class="sub-title">Preskool template includes various features for providers such as student list, teachers list, department,etc.</p>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" target="_blank" href="template-rtl/teachers.html"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/teachers.html" target="_blank"><img src="assets/img/image-01.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/teachers.html" target="_blank">Teachers List</a></div>
					</div>
				</div>

				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/teacher-details.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/teacher-details.html" target="_blank"><img src="assets/img/image-02.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/teacher-details.html" target="_blank">Teachers View</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/departments.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/departments.html" target="_blank"><img src="assets/img/image-03.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/departments.html" target="_blank">Departments</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/subjects.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/subjects.html" target="_blank"><img src="assets/img/image-04.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/subjects.html" target="_blank">Subjects</a></div>
					</div>
				</div>

				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/fees.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/fees.html" target="_blank"><img src="assets/img/image-05.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/fees.html" target="_blank">Fees</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/alerts.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/alerts.html" target="_blank"><img src="assets/img/image-06.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/alerts.html" target="_blank"> Base UI</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/salary.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/salary.html" target="_blank"><img src="assets/img/image-07.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/salary.html" target="_blank">Salary</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/holiday.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/holiday.html" target="_blank"><img src="assets/img/image-08.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/holiday.html" target="_blank">Holiday</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/event.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/event.html" target="_blank"><img src="assets/img/image-09.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/event.html" target="_blank">Events</a></div>
					</div>
				</div>
				<div class="col-lg-6 col-md-6 col-sm-6 mb-30">
					<div class="project-content wow fadeInUp" data-wow-delay="0.2s">
						<div class="project-link"><a class="button" href="template-rtl/library.html" target="_blank"> View Demo</a></div>
						<a class="overlay-link" href="template-rtl/library.html" target="_blank"><img src="assets/img/image-10.jpg" alt="Project Image" /></a>
					</div>
					<div class="project-info">
						<div class="project-title"><a href="template-rtl/library.html" target="_blank">Library</a></div>
					</div>
				</div>
			</div>			
			
		</div>
	</section>

	<footer class="footer">
			
			<!-- Footer Bottom -->
			<div class="footer-bottom">
				<div class="container-fluid">
					<!-- Copyright -->
					<div class="copyright">
						<div class="row">
							<div class="col-md-12 text-center">
								<div class="copyright-text">
									<p class="mb-0">© 2022 <a href="index.html">Preskool</a>. All rights reserved.</p>
								</div>
							</div>
						</div>
					</div>
					<!-- /Copyright -->
				</div>
			</div>
			<!-- /Footer Bottom -->
			
		</footer>


	<script src="assets/js/jquery-3.6.0.min.js"></script>
	<script src="assets/js/bootstrap.bundle.min.js"></script>
	<script src="assets/js/wow.min.js"></script>

	<script src="assets/js/main.js"></script>

	<!--Start of Tawk.to Script-->
	<script type="text/javascript">
		var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
		(function(){
		var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
		s1.async=true;
		s1.src='https://embed.tawk.to/5d8a11a26c1dde20ed0329dd/default';
		s1.charset='UTF-8';
		s1.setAttribute('crossorigin','*');
		s0.parentNode.insertBefore(s1,s0);
		})();
	</script>
	<!--End of Tawk.to Script-->
	
</body>
</html>