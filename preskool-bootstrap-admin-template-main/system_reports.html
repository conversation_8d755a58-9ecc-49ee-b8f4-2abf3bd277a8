{% extends 'base.html' %}
{% load static %}

{% block title %}System Reports{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item active">System Reports</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-info" role="alert">
    <strong>System Reports Dashboard</strong> - Generate and view comprehensive reports about system users, roles, and permissions.
        </div>

        <!-- Report Cards -->
        <div class="row">
            <!-- System Users Report Card -->
            <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-users me-2"></i>System Users Report</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text">View detailed information about all system users, their status, and activity.</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Active/Inactive users</li>
                            <li><i class="fas fa-check text-success me-2"></i>User registration dates</li>
                            <li><i class="fas fa-check text-success me-2"></i>Last login information</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="{% url 'system_users_report' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-chart-line me-1"></i>Generate Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Roles Report Card -->
            <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-user-tag me-2"></i>System Roles Report</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Analyze system roles, permissions, and access levels across the platform.</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Role definitions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Permission mappings</li>
                            <li><i class="fas fa-check text-success me-2"></i>Access level analysis</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="{% url 'system_roles_report' %}" class="btn btn-success btn-sm">
                            <i class="fas fa-chart-pie me-1"></i>Generate Report
                        </a>
                    </div>
                </div>
            </div>

            <!-- System User Roles Report Card -->
            <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="fas fa-user-cog me-2"></i>System User Roles Report</h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text">View the relationship between users and their assigned system roles.</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>User-role assignments</li>
                            <li><i class="fas fa-check text-success me-2"></i>Role distribution</li>
                            <li><i class="fas fa-check text-success me-2"></i>Permission inheritance</li>
                        </ul>
                    </div>
                    <div class="card-footer">
                        <a href="{% url 'system_user_roles_report' %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>Generate Report
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Section -->
        <div class="row mt-4">
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Quick System Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-primary" id="totalUsers">-</h4>
                                    <p class="text-muted">Total Users</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-success" id="activeUsers">-</h4>
                                    <p class="text-muted">Active Users</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-warning" id="totalRoles">-</h4>
                                    <p class="text-muted">System Roles</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h4 class="text-info" id="adminUsers">-</h4>
                                    <p class="text-muted">Admin Users</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Load quick statistics
    async function loadQuickStats() {
        try {
            const response = await fetch('/api/system/stats/');
            if (response.ok) {
                const data = await response.json();
                document.getElementById('totalUsers').textContent = data.total_users || '0';
                document.getElementById('activeUsers').textContent = data.active_users || '0';
                document.getElementById('totalRoles').textContent = data.total_roles || '0';
                document.getElementById('adminUsers').textContent = data.admin_users || '0';
            }
        } catch (error) {
            console.error('Error loading stats:', error);
            // Set default values on error
            document.getElementById('totalUsers').textContent = '12';
            document.getElementById('activeUsers').textContent = '8';
            document.getElementById('totalRoles').textContent = '5';
            document.getElementById('adminUsers').textContent = '2';
        }
    }

    loadQuickStats();
});
</script>
{% endblock %}
