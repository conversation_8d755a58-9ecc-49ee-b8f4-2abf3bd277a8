{% extends 'base.html' %}
{% load static %}

{% block title %}Deactivate Status{% endblock %}

{% block content %}
        <div class="page-header">
    <div class="row">
                <div class="col-sm-12">
                        <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'home' %}"><i class="fa fa-home me-1"></i>Home</a></li>
                <li class="breadcrumb-item active">Deactivate Status</li>
                        </ul>
                    </div>
                </div>
            </div>

<div class="alert alert-info" role="alert">
    View and manage user deactivation status. You can see which users are active or deactivated in the system.
        </div>

        <div class="row">
    <div class="col-xs-12 col-sm-12">
        <form class="form-horizontal" id="status-search-form">
            <div class="form-group row align-items-center">
                <label class="col-sm-3 col-form-label text-sm-end">Search Users</label>
                <div class="col-sm-5">
                    <input id="statusQuery" class="form-control" type="text" placeholder="Enter user name to check status">
                </div>
                <div class="col-sm-4 text-sm-start text-end mt-3 mt-sm-0">
                    <button type="submit" class="btn btn-info">
                        Check Status <i class="fa fa-search ms-1"></i>
                    </button>
                                </div>
                            </div>
                        </form>
            </div>
        </div>

        <div class="row mt-4" id="statusResultsSection" style="display:none;">
            <div class="col-sm-12">
                <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">User Status Results</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="statusResultsTable">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Full Name</th>
                                        <th>Post</th>
                                        <th>Department/Section</th>
                                        <th>Campus</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="statusResultsBody"></tbody>
                        </table>
                    </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('status-search-form');
  const queryInput = document.getElementById('statusQuery');
  const resultsSection = document.getElementById('statusResultsSection');
  const resultsBody = document.getElementById('statusResultsBody');

  async function fetchStatusResults(q) {
    try {
      const resp = await fetch(`/api/staff/status/?q=${encodeURIComponent(q)}`);
      if (!resp.ok) throw new Error('Network response was not ok');
      return await resp.json();
    } catch (e) {
      return { results: [] };
    }
  }

  function renderNoResults() {
    resultsBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No users found</td></tr>';
  }

  form.addEventListener('submit', async function (e) {
    e.preventDefault();
    const q = (queryInput.value || '').trim();
    resultsSection.style.display = 'block';

    const data = await fetchStatusResults(q);
    if (!data.results || !data.results.length) {
      renderNoResults();
      return;
    }
    resultsBody.innerHTML = data.results.map((r, i) => {
      const statusBadge = r.is_active ? 
        '<span class="badge bg-success">Active</span>' : 
        '<span class="badge bg-danger">Deactivated</span>';
      
      const actionButton = r.is_active ? 
        '<a href="javascript:void(0);" class="btn btn-danger btn-sm">Deactivate</a>' :
        '<a href="javascript:void(0);" class="btn btn-success btn-sm">Activate</a>';
      
      return (
        '<tr>' +
        `<td>${i + 1}</td>` +
        `<td>${r.full_name}</td>` +
        `<td>${r.post}</td>` +
        `<td>${r.department}</td>` +
        `<td>${r.campus}</td>` +
        `<td>${statusBadge}</td>` +
        `<td>${actionButton}</td>` +
        '</tr>'
      );
    }).join('');
  });
});
</script>
{% endblock %}
