
					<div class="breadcrumbs ace-save-state" id="breadcrumbs">
						<ul class="breadcrumb">
							<li>
								<i class="ace-icon fa fa-home home-icon"></i>
								<a href="#">Home</a>
							</li>
							<li class="active">Add user</li>
						</ul><!-- /.breadcrumb -->

						<div class="nav-search" id="nav-search">
							<form class="form-search">
								<span class="input-icon">
									<input type="text" placeholder="Search ..." class="nav-search-input" id="nav-search-input" autocomplete="off">
									<i class="ace-icon fa fa-search nav-search-icon"></i>
								</span>
							</form>
						</div><!-- /.nav-search -->
			      </div>

					<div class="page-content">


						<div class="row">
							<div class="col-xs-12">
								<!-- PAGE CONTENT BEGINS -->
								<div class="alert alert-block alert-success">
						
									Select a System and submit to view Reports.
								</div>


								<!-- PAGE CONTENT ENDS -->
								<div class="hr hr32 hr-dotted"></div>
							</div>
							
							
							<!-- /.col -->
						</div><!-- /.row -->
					</div>
                    
                    <div class="page-content">

						<div class="row">
							<div class="col-xs-12">
                                
                                <form class="form-horizontal" role="form" action="" method="POST">
									
                                    <div class="form-group">
										<label class="col-sm-3 control-label no-padding-right" for="form-field-1"> Staff Name </label>

										<div class="col-sm-5">
											<select class="chosen-select form-control" id="form-field-select-3" data-placeholder="Choose a System" name="systems">
								                  <option value="Ac. Calendar">Ac. Calendar</option><option value="Academics">Academics</option><option value="Academic_Year_Configuration">Academic_Year_Configuration</option><option value="Admissions">Admissions</option><option value="Alumni Manager">Alumni Manager</option><option value="Applications">Applications</option><option value="Applications Admin">Applications Admin</option><option value="Assessment">Assessment</option><option value="Audit">Audit</option><option value="Bookings">Bookings</option><option value="Curriculum">Curriculum</option><option value="Curriculum Reports">Curriculum Reports</option><option value="Curriculum_courses_configuration">Curriculum_courses_configuration</option><option value="Dean Curriculum">Dean Curriculum</option><option value="Deans">Deans</option><option value="Deffered">Deffered</option><option value="DRO">DRO</option><option value="E-Approvals">E-Approvals</option><option value="E-Forms">E-Forms</option><option value="Fees Clearance">Fees Clearance</option><option value="Graduation">Graduation</option><option value="Heads">Heads</option><option value="Help Desk">Help Desk</option><option value="Helpdesk">Helpdesk</option><option value="Hostel Management">Hostel Management</option><option value="Housing &amp; Catering">Housing &amp; Catering</option><option value="HRM">HRM</option><option value="LUMIS">LUMIS</option><option value="Management Reports">Management Reports</option><option value="Management Students Report">Management Students Report</option><option value="ODL APPLICATIONS">ODL APPLICATIONS</option><option value="Offical Documents">Offical Documents</option><option value="Office 365">Office 365</option><option value="Organisation">Organisation</option><option value="Organization Documents">Organization Documents</option><option value="Partners">Partners</option><option value="Password">Password</option><option value="PG Applications">PG Applications</option><option value="PG Coordinator">PG Coordinator</option><option value="PG Tracking">PG Tracking</option><option value="PMS">PMS</option><option value="PMS-Self Evaluation">PMS-Self Evaluation</option><option value="PRO">PRO</option><option value="Procurement System">Procurement System</option><option value="Profile">Profile</option><option value="Project Tracking">Project Tracking</option><option value="Project Tracking Admin">Project Tracking Admin</option><option value="Research &amp; Outrearch">Research &amp; Outrearch</option><option value="Risk Assessment">Risk Assessment</option><option value="Scholarships">Scholarships</option><option value="SL-Forum">SL-Forum</option><option value="SLF">SLF</option><option value="Staff Portal">Staff Portal</option><option value="Students">Students</option><option value="Students Union">Students Union</option><option value="Students Works">Students Works</option><option value="Study Circles">Study Circles</option><option value="Supervision">Supervision</option><option value="Ticket Management">Ticket Management</option><option value="Timetable">Timetable</option><option value="Tutorial Request">Tutorial Request</option><option value="Users">Users</option><option value="VMS">VMS</option>								            </select>
										</div>
									</div>
                                    
                                    
                                    <div class="form-group">
										

										<div class="col-sm-9" style="float: right">
											<button class="btn btn-info" type="submit" name="submit">
												
												Submit <i class="ace-icon fa fa-arrow-right icon-on-right bigger-110"></i>
											</button>
										</div>
									</div>
                                    
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="page-content">

						<div class="row">
							<div class="col-xs-12">
                                
                                                          </div>
                        </div>
                    </div>
                    
                    