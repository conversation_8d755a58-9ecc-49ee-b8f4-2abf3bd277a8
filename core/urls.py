from django.urls import path
from . import views


urlpatterns = [
    path('', views.home, name='home'),
    path('add-users/', views.add_users, name='add_users'),
    path('password-reset/', views.password_reset, name='password_reset'),
    path('deactivate/', views.deactivate_user, name='deactivate_user'),
    path('deactivate-status/', views.deactivate_status, name='deactivate_status'),
    path('search-user/', views.search_user, name='search_user'),
    path('user-system/', views.user_system, name='user_system'),

    # System Reports URLs
    path('system-reports/', views.system_reports, name='system_reports'),
    path('system-reports/users/', views.system_users_report, name='system_users_report'),
    path('system-reports/roles/', views.system_roles_report, name='system_roles_report'),
    path('system-reports/user-roles/', views.system_user_roles_report, name='system_user_roles_report'),

    # API endpoints
    path('api/staff/search/', views.staff_search, name='staff_search'),
    path('api/staff/status/', views.staff_status_search, name='staff_status_search'),
    path('api/staff/system/', views.staff_system_search, name='staff_system_search'),
    path('api/system/stats/', views.system_stats_api, name='system_stats_api'),
    path('api/reports/users/', views.users_report_api, name='users_report_api'),
    path('api/reports/roles/', views.roles_report_api, name='roles_report_api'),
    path('api/reports/user-roles/', views.user_roles_report_api, name='user_roles_report_api'),
]


