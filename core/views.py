from django.shortcuts import render
from django.http import JsonResponse


def home(request):
    return render(request, 'home.html')


def add_users(request):
    return render(request, 'add_users_django.html')


def password_reset(request):
    return render(request, 'possword_reset.html')


def deactivate_user(request):
    return render(request, 'deactivat.html')


def deactivate_status(request):
    return render(request, 'deactivate-status.html')


def search_user(request):
    return render(request, 'search_user.html')


def user_system(request):
    return render(request, 'user_system.html')


# System Reports Views
def system_reports(request):
    return render(request, 'system_reports.html')


def system_users_report(request):
    return render(request, 'system_users_report.html')


def system_roles_report(request):
    return render(request, 'system_roles_report.html')


def system_user_roles_report(request):
    return render(request, 'system_user_roles_report.html')


def staff_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': '<PERSON><PERSON> Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
            }
        ]
    return JsonResponse({'results': results})


def staff_status_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': 'Chihana Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
                'is_active': True,
            }
        ]
    elif query and 'john' in query:
        results = [
            {
                'full_name': 'John Doe',
                'post': 'Teacher',
                'department': 'EDUCATION',
                'campus': 'MAIN',
                'is_active': False,
            }
        ]
    return JsonResponse({'results': results})


def staff_system_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': 'Chihana Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
                'is_active': True,
                'system_role': 'Administrator',
            }
        ]
    elif query and 'john' in query:
        results = [
            {
                'full_name': 'John Doe',
                'post': 'Teacher',
                'department': 'EDUCATION',
                'campus': 'MAIN',
                'is_active': False,
                'system_role': 'User',
            }
        ]
    elif query and 'mary' in query:
        results = [
            {
                'full_name': 'Mary Smith',
                'post': 'Librarian',
                'department': 'LIBRARY',
                'campus': 'BND',
                'is_active': True,
                'system_role': 'Moderator',
            }
        ]
    return JsonResponse({'results': results})


# API endpoints for reports
def system_stats_api(request):
    stats = {
        'total_users': 12,
        'active_users': 8,
        'total_roles': 5,
        'admin_users': 2
    }
    return JsonResponse(stats)


def users_report_api(request):
    status_filter = request.GET.get('status', '')
    department_filter = request.GET.get('department', '')
    campus_filter = request.GET.get('campus', '')

    # Sample data - in real app, this would query the database
    all_users = [
        {
            'full_name': 'Chihana Wakisa',
            'post': 'WebMaster',
            'department': 'ADMIN',
            'campus': 'BND',
            'is_active': True,
            'system_role': 'Administrator',
            'last_login': '2025-10-01',
            'registration_date': '2025-01-15'
        },
        {
            'full_name': 'John Doe',
            'post': 'Teacher',
            'department': 'EDUCATION',
            'campus': 'MAIN',
            'is_active': False,
            'system_role': 'User',
            'last_login': '2025-09-15',
            'registration_date': '2025-02-20'
        },
        {
            'full_name': 'Mary Smith',
            'post': 'Librarian',
            'department': 'LIBRARY',
            'campus': 'BND',
            'is_active': True,
            'system_role': 'Moderator',
            'last_login': '2025-10-02',
            'registration_date': '2025-03-10'
        }
    ]

    # Apply filters
    filtered_users = all_users
    if status_filter:
        filtered_users = [u for u in filtered_users if (u['is_active'] and status_filter == 'active') or (not u['is_active'] and status_filter == 'inactive')]
    if department_filter:
        filtered_users = [u for u in filtered_users if u['department'] == department_filter]
    if campus_filter:
        filtered_users = [u for u in filtered_users if u['campus'] == campus_filter]

    return JsonResponse({'results': filtered_users})


def roles_report_api(request):
    roles_data = [
        {
            'name': 'Administrator',
            'description': 'Full system access and management',
            'permission_level': 'High',
            'level_color': 'danger',
            'user_count': 2,
            'key_permissions': ['User Management', 'System Config', 'Reports'],
            'is_active': True
        },
        {
            'name': 'Moderator',
            'description': 'Content and user moderation',
            'permission_level': 'Medium',
            'level_color': 'warning',
            'user_count': 3,
            'key_permissions': ['Content Moderation', 'User Support'],
            'is_active': True
        },
        {
            'name': 'User',
            'description': 'Standard user access',
            'permission_level': 'Low',
            'level_color': 'info',
            'user_count': 7,
            'key_permissions': ['Profile Management', 'Basic Access'],
            'is_active': True
        }
    ]
    return JsonResponse({'results': roles_data})


def user_roles_report_api(request):
    view_type = request.GET.get('view_type', 'user_roles')
    role_filter = request.GET.get('role', '')

    if view_type == 'user_roles':
        data = [
            {
                'user_name': 'Chihana Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'role_name': 'Administrator',
                'role_level': 'High',
                'role_level_color': 'danger',
                'assignment_date': '2025-01-15',
                'is_active': True
            },
            {
                'user_name': 'Mary Smith',
                'post': 'Librarian',
                'department': 'LIBRARY',
                'role_name': 'Moderator',
                'role_level': 'Medium',
                'role_level_color': 'warning',
                'assignment_date': '2025-03-10',
                'is_active': True
            }
        ]
    elif view_type == 'role_users':
        data = [
            {
                'role_name': 'Administrator',
                'permission_level': 'High',
                'level_color': 'danger',
                'users': ['Chihana Wakisa', 'Admin User'],
                'user_count': 2,
                'is_active': True
            },
            {
                'role_name': 'Moderator',
                'permission_level': 'Medium',
                'level_color': 'warning',
                'users': ['Mary Smith', 'John Moderator', 'Jane Mod'],
                'user_count': 3,
                'is_active': True
            }
        ]
    else:  # summary
        data = [
            {'role_name': 'Administrator', 'user_count': 2, 'percentage': 16.7, 'is_active': True},
            {'role_name': 'Moderator', 'user_count': 3, 'percentage': 25.0, 'is_active': True},
            {'role_name': 'User', 'user_count': 7, 'percentage': 58.3, 'is_active': True}
        ]

    summary = {
        'total_assignments': 12,
        'active_assignments': 10,
        'unique_roles': 3,
        'users_with_roles': 12
    }

    return JsonResponse({'results': data, 'summary': summary})
