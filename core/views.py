from django.shortcuts import render
from django.http import JsonResponse


def home(request):
    return render(request, 'home.html')


def add_users(request):
    return render(request, 'add_users_django.html')


def password_reset(request):
    return render(request, 'possword_reset.html')


def deactivate_user(request):
    return render(request, 'deactivat.html')


def deactivate_status(request):
    return render(request, 'deactivate-status.html')


def search_user(request):
    return render(request, 'search_user.html')


def user_system(request):
    return render(request, 'user_system.html')


def staff_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': '<PERSON><PERSON> Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
            }
        ]
    return JsonResponse({'results': results})


def staff_status_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': '<PERSON><PERSON> Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
                'is_active': True,
            }
        ]
    elif query and 'john' in query:
        results = [
            {
                'full_name': 'John Doe',
                'post': 'Teacher',
                'department': 'EDUCATION',
                'campus': 'MAIN',
                'is_active': False,
            }
        ]
    return JsonResponse({'results': results})


def staff_system_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': 'Chihana Wakisa',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
                'is_active': True,
                'system_role': 'Administrator',
            }
        ]
    elif query and 'john' in query:
        results = [
            {
                'full_name': 'John Doe',
                'post': 'Teacher',
                'department': 'EDUCATION',
                'campus': 'MAIN',
                'is_active': False,
                'system_role': 'User',
            }
        ]
    elif query and 'mary' in query:
        results = [
            {
                'full_name': 'Mary Smith',
                'post': 'Librarian',
                'department': 'LIBRARY',
                'campus': 'BND',
                'is_active': True,
                'system_role': 'Moderator',
            }
        ]
    return JsonResponse({'results': results})
