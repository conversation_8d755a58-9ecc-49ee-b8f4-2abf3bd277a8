from django.shortcuts import render
from django.http import JsonResponse


def home(request):
    return render(request, 'home.html')


def add_users(request):
    return render(request, 'add_users_django.html')


def password_reset(request):
    return render(request, 'possword_reset.html')


def deactivate_user(request):
    return render(request, 'deactivat.html')


def deactivate_status(request):
    return render(request, 'deactivate-status.html')


def staff_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': '<PERSON><PERSON> Waki<PERSON>',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
            }
        ]
    return JsonResponse({'results': results})


def staff_status_search(request):
    query = (request.GET.get('q') or '').strip().lower()
    results = []
    if query and 'wakisa' in query:
        results = [
            {
                'full_name': '<PERSON><PERSON>',
                'post': 'WebMaster',
                'department': 'ADMIN',
                'campus': 'BND',
                'is_active': True,
            }
        ]
    elif query and 'john' in query:
        results = [
            {
                'full_name': '<PERSON> Doe',
                'post': 'Teacher',
                'department': 'EDUCATION',
                'campus': 'MAIN',
                'is_active': False,
            }
        ]
    return JsonResponse({'results': results})
